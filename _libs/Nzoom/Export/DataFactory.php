<?php

namespace Nzoom\Export;

use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\Export\Provider\ExportTableProviderInterface;

/**
 * Class DataFactory
 *
 * Factory for creating ExportData instances from models and outlook
 */
class DataFactory
{
    /**
     * @var \Registry The registry
     */
    private $registry;

    /**
     * @var int Chunk size for processing models
     */
    private $chunkSize = 50;

    /**
     * @var ExportTableProviderInterface|null Table provider for extracting related tables
     */
    private $tableProvider;

    /**
     * DataFactory constructor
     *
     * @param \Registry $registry The registry
     */
    public function __construct(\Registry $registry)
    {
        $this->registry = $registry;
    }

    /**
     * Set table provider for extracting related tables from models
     *
     * @param ExportTableProviderInterface|null $tableProvider
     */
    public function setTableProvider(?ExportTableProviderInterface $tableProvider): void
    {
        $this->tableProvider = $tableProvider;
    }

    /**
     * Check if table extraction is enabled
     *
     * @return bool
     */
    public function isTablesEnabled(): bool
    {
        return $this->tableProvider !== null;
    }

    /**
     * Create and configure a ModelTableProvider with the given options
     *
     * @param array $options Table provider options (include_empty_tables, date_format, etc.)
     * @return self Returns this instance for method chaining
     */
    public function withModelTableProvider(array $options = []): self
    {
        $provider = new \Nzoom\Export\Provider\ModelTableProvider($this->registry, $options);
        $this->setTableProvider($provider);
        return $this;
    }

    /**
     * Create an ExportData instance from models and outlook
     *
     * @param \Model[] $models Array of models
     * @param \Outlook $outlook The outlook
     * @return ExportData The created export data
     */
    public function __invoke(array $models, \Outlook $outlook): ExportData
    {
        // Create header from outlook
        $header = $this->createHeaderFromOutlook($outlook);

        // Create export data with header
        $exportData = new ExportData($header, [
            'generated_at' => new \DateTime(),
            'generated_by' => $this->registry['currentUser']->get('id'),
        ]);



        // Process models in chunks
        $chunks = array_chunk($models, $this->chunkSize);
        foreach ($chunks as $chunk) {
            $this->processModelsChunk($chunk, $exportData, $header);
        }

        return $exportData;
    }

    /**
     * Create header from outlook
     *
     * @param \Outlook $outlook The outlook
     * @return ExportHeader The created header
     */
    private function createHeaderFromOutlook(\Outlook $outlook): ExportHeader
    {
        $header = new ExportHeader('f0f0f0', ['font-weight' => 'bold']);
        $fields = $outlook->get('current_custom_fields');

        if (!empty($fields)) {
            foreach ($fields as $field) {
                // Skip fields that explicitly have position set to 0 or false
                if (isset($field['position']) && !$field['position']) {
                    // position explicitly set to 0 or false means not visible
                    continue;
                }
                $type = $this->mapFieldTypeToExportType($field['field_type'] ?? 'text');
                $width = !empty($field['column_width']) ? (int)$field['column_width'] : null;

                $header->addColumn(new ExportColumn(
                    $field['name'],
                    $field['label'],
                    $type,
                    '',
                    $width
                ));
            }
        }

        return $header;
    }

    /**
     * Process a chunk of models
     *
     * @param \Model[] $models Array of models
     * @param ExportData $exportData The export data to add records to
     * @param ExportHeader $header The header to validate against
     */
    private function processModelsChunk(array $models, ExportData $exportData, ExportHeader $header): void
    {
        foreach ($models as $model) {
            $exportData->addRecord($this->createRecordFromModel($model, $header), true);
        }
    }

    /**
     * Create a record from a model
     *
     * @param \Model $model The model
     * @param ExportHeader $header The header to get columns from
     * @return ExportRecord The created record
     */
    private function createRecordFromModel(\Model $model, ExportHeader $header): ExportRecord
    {
        $metadata = [
            'id' => $model->get('id')
        ];

        // Try to get full_num from the model using various methods
        $fullNum = $this->getModelFullNum($model);
        if ($fullNum !== null) {
            $metadata['full_num'] = $fullNum;
        }

        $record = new ExportRecord($metadata);

        foreach ($header as $column) {
            $varName = $column->getVarName();

            // Use the new method to get the value with arguments
            $value = $model->getExportVarValueWithArgs($varName);

            // Get type and format from the column (from outlook configuration)
            $type = $column->getType();
            $format = $column->getFormat();

            // Try to get more specific type information from the model
            if (method_exists($model, 'getExportVarType')) {
                $modelType = $model->getExportVarType($varName);
                if (!empty($modelType)) {
                    $type = $modelType;
                }
            }

            $record->addValue($varName, $value, $type, $format);
        }

        // Extract tables if table provider is configured
        if ($this->tableProvider && $this->isTablesEnabled()) {
            $this->extractTablesForRecord($record, $model);
        }

        return $record;
    }

    /**
     * Extract tables for a record using the configured table provider
     *
     * @param ExportRecord $record The export record to add tables to
     * @param \Model $model The source model
     */
    private function extractTablesForRecord(ExportRecord $record, \Model $model): void
    {
        try {
            $tableCollection = $this->tableProvider->getTablesForRecord($model);

            if ($tableCollection && $tableCollection->hasTables()) {
                $record->setTableCollection($tableCollection);
            }
        } catch (\Exception $e) {
            // Log error but don't fail the export
            if (isset($this->registry['logger'])) {
                $this->registry['logger']->warning(
                    'Failed to extract tables for model ID ' . $model->get('id') . ': ' . $e->getMessage()
                );
            }
        }
    }

    /**
     * Map field type to export value type
     *
     * @param string $fieldType The field type
     * @return string The export value type
     */
    private function mapFieldTypeToExportType(string $fieldType): string
    {
        $typeMap = [
            'text' => ExportValue::TYPE_STRING,
            'textarea' => ExportValue::TYPE_STRING,
            'richtext' => ExportValue::TYPE_STRING,
            'int' => ExportValue::TYPE_INTEGER,
            'integer' => ExportValue::TYPE_INTEGER,
            'float' => ExportValue::TYPE_FLOAT,
            'decimal' => ExportValue::TYPE_FLOAT,
            'money' => ExportValue::TYPE_FLOAT,
            'bool' => ExportValue::TYPE_BOOLEAN,
            'boolean' => ExportValue::TYPE_BOOLEAN,
            'checkbox' => ExportValue::TYPE_BOOLEAN,
            'date' => ExportValue::TYPE_DATE,
            'datetime' => ExportValue::TYPE_DATETIME,
            'timestamp' => ExportValue::TYPE_DATETIME,
            'array' => ExportValue::TYPE_ARRAY,
            'json' => ExportValue::TYPE_ARRAY,
        ];

        return $typeMap[$fieldType] ?? ExportValue::TYPE_STRING;
    }

    /**
     * Set the chunk size
     *
     * @param int $chunkSize The chunk size
     */
    public function setChunkSize(int $chunkSize)
    {
        $this->chunkSize = $chunkSize;
    }

    /**
     * Create a streaming ExportData instance that loads records lazily
     *
     * @param string $modelClass The model class name (e.g., 'Finance_Incomes')
     * @param array $filters Database filters for the model search
     * @param \Outlook $outlook The outlook configuration
     * @param int $pageSize Number of records to load per page
     * @return ExportData The streaming export data instance
     */
    public function createStreaming(string $factoryClass, array $filters, \Outlook $outlook, int $pageSize = 200): ExportData
    {
        // Create header from outlook
        $header = $this->createHeaderFromOutlook($outlook);

        // Create export data with header
        $exportData = new ExportData($header, [
            'generated_at' => new \DateTime(),
            'generated_by' => $this->registry['currentUser']->get('id'),
        ]);



        // Create record provider function
        $recordProvider = function (int $offset, int $limit) use ($factoryClass, $filters, $header) {
            // Add pagination to filters
            $paginatedFilters = array_merge($filters, [
                'limit' => $limit,
                'offset' => $offset
            ]);

            // Get models for this page
            $models = $factoryClass::search($this->registry, $paginatedFilters);

            // Convert models to export records
            $records = [];
            foreach ($models as $model) {
                $records[] = $this->createRecordFromModel($model, $header);
            }

            return $records;
        };

        // Configure lazy loading
        $exportData->setRecordProvider($recordProvider, $pageSize);

        return $exportData;
    }

    /**
     * Create a cursor-based streaming ExportData instance for better performance with large datasets
     *
     * @param string $modelClass The model class name
     * @param array $filters Database filters for the model search
     * @param \Outlook $outlook The outlook configuration
     * @param int $pageSize Number of records to load per page
     * @param string $cursorField Field to use for cursor pagination (default: 'id')
     * @return ExportData The streaming export data instance
     */
    public function createCursorStreaming(string $modelClass, array $filters, \Outlook $outlook, int $pageSize = 1000, string $cursorField = 'id'): ExportData
    {
        // Create header from outlook
        $header = $this->createHeaderFromOutlook($outlook);

        // Create export data with header
        $exportData = new ExportData($header, [
            'generated_at' => new \DateTime(),
            'generated_by' => $this->registry['currentUser']->get('id'),
        ]);



        // Track the last cursor value
        $lastCursorValue = 0;

        // Create cursor-based record provider function
        $recordProvider = function (int $offset, int $limit) use ($modelClass, $filters, $header, $cursorField, &$lastCursorValue) {
            // For cursor pagination, we ignore offset and use the cursor field
            $cursorFilters = $filters;

            // Add cursor condition
            if ($lastCursorValue > 0) {
                $whereConditions = $cursorFilters['where'] ?? [];
                $whereConditions[] = "{$cursorField} > {$lastCursorValue}";
                $cursorFilters['where'] = $whereConditions;
            }

            // Add limit and ordering
            $cursorFilters['limit'] = $limit;
            $cursorFilters['order'] = "{$cursorField} ASC";

            // Get models for this page
            $models = $modelClass::search($this->registry, $cursorFilters);

            if (empty($models)) {
                return [];
            }

            // Update cursor for next iteration
            $lastModel = end($models);
            $lastCursorValue = $lastModel->get($cursorField);

            // Convert models to export records
            $records = [];
            foreach ($models as $model) {
                $records[] = $this->createRecordFromModel($model, $header);
            }

            return $records;
        };

        // Configure lazy loading
        $exportData->setRecordProvider($recordProvider, $pageSize);

        return $exportData;
    }
}
