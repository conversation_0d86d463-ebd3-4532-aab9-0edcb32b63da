<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Adapter/AbstractExportFormatAdapter.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item"><a href="index.html">Adapter</a></li>
         <li class="breadcrumb-item active">AbstractExportFormatAdapter.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="83.70" aria-valuemin="0" aria-valuemax="100" style="width: 83.70%">
           <span class="sr-only">83.70% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">83.70%</div></td>
       <td class="success small"><div align="right">77&nbsp;/&nbsp;92</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="58.33" aria-valuemin="0" aria-valuemax="100" style="width: 58.33%">
           <span class="sr-only">58.33% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">58.33%</div></td>
       <td class="warning small"><div align="right">7&nbsp;/&nbsp;12</div></td>
       <td class="warning small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter">AbstractExportFormatAdapter</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="83.70" aria-valuemin="0" aria-valuemax="100" style="width: 83.70%">
           <span class="sr-only">83.70% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">83.70%</div></td>
       <td class="success small"><div align="right">77&nbsp;/&nbsp;92</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="58.33" aria-valuemin="0" aria-valuemax="100" style="width: 58.33%">
           <span class="sr-only">58.33% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">58.33%</div></td>
       <td class="warning small"><div align="right">7&nbsp;/&nbsp;12</div></td>
       <td class="warning small">44.26</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#43"><abbr title="__construct(Registry $registry, string $module, string $controller)">__construct</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">5&nbsp;/&nbsp;5</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">2</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#57"><abbr title="setConfiguration(array $config): Nzoom\Export\Adapter\ExportFormatAdapterInterface">setConfiguration</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">2&nbsp;/&nbsp;2</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#70"><abbr title="getExportFilename($prefix, string $extension): string">getExportFilename</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">9&nbsp;/&nbsp;9</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">5</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#100"><abbr title="sendHeaders(string $filename, string $contentType): void">sendHeaders</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;10</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">12</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#127"><abbr title="handleExportError(string $message, int $statusCode): void">handleExportError</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="85.71" aria-valuemin="0" aria-valuemax="100" style="width: 85.71%">
           <span class="sr-only">85.71% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">85.71%</div></td>
       <td class="success small"><div align="right">12&nbsp;/&nbsp;14</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">4.05</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#161"><abbr title="getRecordHeaders($record): array">getRecordHeaders</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">10&nbsp;/&nbsp;10</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">5</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#181"><abbr title="getFormatOptions(): array">getFormatOptions</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#193"><abbr title="validateAndPrepareSaveTarget($file)">validateAndPrepareSaveTarget</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">5&nbsp;/&nbsp;5</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">3</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#213"><abbr title="validateStringTarget(string $target): string">validateStringTarget</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;3</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">2</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#231"><abbr title="validatePhpStreamWrapper(string $wrapper): string">validatePhpStreamWrapper</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="94.12" aria-valuemin="0" aria-valuemax="100" style="width: 94.12%">
           <span class="sr-only">94.12% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">94.12%</div></td>
       <td class="success small"><div align="right">16&nbsp;/&nbsp;17</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">5.01</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#273"><abbr title="validateFilePath(string $filePath): string">validateFilePath</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="83.33" aria-valuemin="0" aria-valuemax="100" style="width: 83.33%">
           <span class="sr-only">83.33% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">83.33%</div></td>
       <td class="success small"><div align="right">5&nbsp;/&nbsp;6</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">3.04</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#295"><abbr title="validateFilePointer($filePointer)">validateFilePointer</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="90.00" aria-valuemin="0" aria-valuemax="100" style="width: 90.00%">
           <span class="sr-only">90.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">90.00%</div></td>
       <td class="success small"><div align="right">9&nbsp;/&nbsp;10</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">4.02</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">Nzoom</span><span class="default">\</span><span class="default">Export</span><span class="default">\</span><span class="default">Adapter</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Nzoom</span><span class="default">\</span><span class="default">I18n</span><span class="default">\</span><span class="default">I18nAwareTrait</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;Abstract&nbsp;base&nbsp;class&nbsp;for&nbsp;export&nbsp;format&nbsp;adapters</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;Provides&nbsp;common&nbsp;functionality&nbsp;that&nbsp;can&nbsp;be&nbsp;shared&nbsp;across&nbsp;different&nbsp;export&nbsp;format&nbsp;adapters.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">abstract</span><span class="default">&nbsp;</span><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">AbstractExportFormatAdapter</span><span class="default">&nbsp;</span><span class="keyword">implements</span><span class="default">&nbsp;</span><span class="default">ExportFormatAdapterInterface</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">I18nAwareTrait</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;\Registry</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">$registry</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">$module</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">$controller</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">$configuration</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;AbstractExportFormatAdapter&nbsp;constructor</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;\Registry&nbsp;$registry</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$module</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$controller</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">__construct</span><span class="keyword">(</span><span class="default">\</span><span class="default">Registry</span><span class="default">&nbsp;</span><span class="default">$registry</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$module</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$controller</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="163 tests cover line 45" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetDecimalPlaces&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testFormatDateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testFormatValueForCsv&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testNormalizeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUSDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testDateFormatSettersAndGetters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddNamedRangesWithInvalidVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddNamedRanges&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetDocumentProperties&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testConvertCustomDateFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testConvertCustomNumberFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithDifferentCellPositions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithPhpSpreadsheetNotAvailable&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExtractSizingOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExtractSizingOptionsWithDefaults&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorIntegrationWithProcessExportRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorPreservesOtherCells&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithRecordInformation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testOptimizeMemoryForExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateWriter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateWriterWithUnsupportedFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetCellValueWithFormatting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testStyleHeaderRow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatFromExportValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatFromExportValueWithCustomFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetSpreadsheetLocale&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testFormatDateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testFormatValueForJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateCsvAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateExcelAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testFormatTrimming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCaseInsensitiveFormatHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterCachingWithDifferentOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterCaching&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterWithOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateJsonAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithUppercaseExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testAdapterInstancesAreProperlyConfigured&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testFactoryWithDifferentModuleAndController&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOnlyExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithComplexPath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testMultipleFormatSupport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testEmptyOptionsHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testAdapterConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCacheKeyGeneration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testComplexCachingScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSendHeadersWithDefaultContentType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSendHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConstructor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidDirectory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithWrongResourceType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConcreteImplementationMethods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testExportMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetVarsMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfigurationMerging&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConstructorWithoutTranslator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDifferentExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetAllMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$registry</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="163 tests cover line 46" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetDecimalPlaces&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testFormatDateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testFormatValueForCsv&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testNormalizeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUSDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testDateFormatSettersAndGetters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddNamedRangesWithInvalidVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddNamedRanges&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetDocumentProperties&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testConvertCustomDateFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testConvertCustomNumberFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithDifferentCellPositions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithPhpSpreadsheetNotAvailable&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExtractSizingOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExtractSizingOptionsWithDefaults&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorIntegrationWithProcessExportRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorPreservesOtherCells&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithRecordInformation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testOptimizeMemoryForExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateWriter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateWriterWithUnsupportedFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetCellValueWithFormatting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testStyleHeaderRow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatFromExportValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatFromExportValueWithCustomFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetSpreadsheetLocale&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testFormatDateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testFormatValueForJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateCsvAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateExcelAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testFormatTrimming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCaseInsensitiveFormatHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterCachingWithDifferentOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterCaching&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterWithOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateJsonAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithUppercaseExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testAdapterInstancesAreProperlyConfigured&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testFactoryWithDifferentModuleAndController&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOnlyExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithComplexPath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testMultipleFormatSupport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testEmptyOptionsHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testAdapterConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCacheKeyGeneration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testComplexCachingScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSendHeadersWithDefaultContentType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSendHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConstructor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidDirectory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithWrongResourceType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConcreteImplementationMethods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testExportMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetVarsMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfigurationMerging&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConstructorWithoutTranslator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDifferentExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetAllMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">module</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$module</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="163 tests cover line 47" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetDecimalPlaces&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testFormatDateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testFormatValueForCsv&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testNormalizeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUSDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testDateFormatSettersAndGetters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddNamedRangesWithInvalidVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddNamedRanges&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetDocumentProperties&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testConvertCustomDateFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testConvertCustomNumberFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithDifferentCellPositions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithPhpSpreadsheetNotAvailable&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExtractSizingOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExtractSizingOptionsWithDefaults&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorIntegrationWithProcessExportRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorPreservesOtherCells&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithRecordInformation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testOptimizeMemoryForExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateWriter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateWriterWithUnsupportedFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetCellValueWithFormatting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testStyleHeaderRow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatFromExportValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatFromExportValueWithCustomFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetSpreadsheetLocale&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testFormatDateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testFormatValueForJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateCsvAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateExcelAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testFormatTrimming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCaseInsensitiveFormatHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterCachingWithDifferentOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterCaching&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterWithOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateJsonAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithUppercaseExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testAdapterInstancesAreProperlyConfigured&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testFactoryWithDifferentModuleAndController&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOnlyExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithComplexPath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testMultipleFormatSupport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testEmptyOptionsHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testAdapterConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCacheKeyGeneration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testComplexCachingScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSendHeadersWithDefaultContentType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSendHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConstructor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidDirectory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithWrongResourceType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConcreteImplementationMethods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testExportMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetVarsMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfigurationMerging&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConstructorWithoutTranslator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDifferentExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetAllMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">controller</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$controller</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="163 tests cover line 49" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetDecimalPlaces&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testFormatDateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testFormatValueForCsv&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testNormalizeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUSDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testDateFormatSettersAndGetters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddNamedRangesWithInvalidVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddNamedRanges&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetDocumentProperties&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testConvertCustomDateFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testConvertCustomNumberFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithDifferentCellPositions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithPhpSpreadsheetNotAvailable&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExtractSizingOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExtractSizingOptionsWithDefaults&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorIntegrationWithProcessExportRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorPreservesOtherCells&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithRecordInformation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testOptimizeMemoryForExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateWriter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateWriterWithUnsupportedFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetCellValueWithFormatting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testStyleHeaderRow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatFromExportValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatFromExportValueWithCustomFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetSpreadsheetLocale&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testFormatDateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testFormatValueForJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateCsvAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateExcelAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testFormatTrimming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCaseInsensitiveFormatHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterCachingWithDifferentOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterCaching&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterWithOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateJsonAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithUppercaseExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testAdapterInstancesAreProperlyConfigured&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testFactoryWithDifferentModuleAndController&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOnlyExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithComplexPath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testMultipleFormatSupport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testEmptyOptionsHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testAdapterConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCacheKeyGeneration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testComplexCachingScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSendHeadersWithDefaultContentType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSendHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConstructor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidDirectory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithWrongResourceType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConcreteImplementationMethods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testExportMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetVarsMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfigurationMerging&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConstructorWithoutTranslator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDifferentExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetAllMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">isset</span><span class="keyword">(</span><span class="default">$registry</span><span class="keyword">[</span><span class="default">'translater'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="143 tests cover line 50" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testGetDecimalPlaces&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testFormatDateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testFormatValueForCsv&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testNormalizeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUSDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testDateFormatSettersAndGetters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddNamedRangesWithInvalidVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddNamedRanges&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetDocumentProperties&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testConvertCustomDateFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testConvertCustomNumberFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithDifferentCellPositions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithPhpSpreadsheetNotAvailable&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExtractSizingOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExtractSizingOptionsWithDefaults&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorIntegrationWithProcessExportRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorPreservesOtherCells&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithRecordInformation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testOptimizeMemoryForExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleExportRecordCellError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateWriter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateWriterWithUnsupportedFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetCellValueWithFormatting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testStyleHeaderRow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatFromExportValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetExcelFormatFromExportValueWithCustomFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testAddHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testSetSpreadsheetLocale&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testFormatDateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testFormatValueForJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetSupportedExtensions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testSupportsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetFormatName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetMimeType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testGetJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSendHeadersWithDefaultContentType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSendHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConstructor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidDirectory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithWrongResourceType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConcreteImplementationMethods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testExportMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetVarsMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfigurationMerging&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testConstructorWithoutTranslator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDifferentExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetAllMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">setTranslator</span><span class="keyword">(</span><span class="default">$registry</span><span class="keyword">[</span><span class="default">'translater'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;{@inheritdoc}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setConfiguration</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$config</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">ExportFormatAdapterInterface</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="14 tests cover line 59" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterCachingWithDifferentOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterWithOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testAdapterConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCacheKeyGeneration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testComplexCachingScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfigurationMerging&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">configuration</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">array_merge</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">configuration</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$config</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="14 tests cover line 60" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterCachingWithDifferentOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCreateAdapterWithOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testAdapterConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testCacheKeyGeneration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Factory\ExportFormatFactoryTest::testComplexCachingScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testSetConfigurationMerging&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;export&nbsp;filename&nbsp;with&nbsp;proper&nbsp;extension</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string|array|null&nbsp;$prefix&nbsp;Filename&nbsp;prefix</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string|null&nbsp;$extension&nbsp;File&nbsp;extension&nbsp;without&nbsp;dot&nbsp;(uses&nbsp;default&nbsp;if&nbsp;null)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;string&nbsp;Filename</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getExportFilename</span><span class="keyword">(</span><span class="default">$prefix</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$extension</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="8 tests cover line 72" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDifferentExtension&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$extension</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 73" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDefaultExtension&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$extension</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getDefaultExtension</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Handle&nbsp;array&nbsp;or&nbsp;null&nbsp;cases</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="8 tests cover line 77" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDifferentExtension&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">is_array</span><span class="keyword">(</span><span class="default">$prefix</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 78" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$prefix</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$prefix</span><span class="keyword">[</span><span class="default">0</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="8 tests cover line 81" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDifferentExtension&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$prefix</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 82" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$prefix</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">strtolower</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">module</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'_'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">controller</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'_export_'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">date</span><span class="keyword">(</span><span class="default">'Y-m-d_H-i-s'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Add&nbsp;extension&nbsp;if&nbsp;not&nbsp;present</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="8 tests cover line 86" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDifferentExtension&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">substr</span><span class="keyword">(</span><span class="default">$prefix</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">-</span><span class="keyword">(</span><span class="default">strlen</span><span class="keyword">(</span><span class="default">$extension</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">+</span><span class="default">&nbsp;</span><span class="default">1</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">'.'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$extension</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 87" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDifferentExtension&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$prefix</span><span class="default">&nbsp;</span><span class="default">.=</span><span class="default">&nbsp;</span><span class="default">'.'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$extension</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="8 tests cover line 90" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDefaultExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetExportFilenameWithDifferentExtension&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$prefix</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Send&nbsp;HTTP&nbsp;headers&nbsp;for&nbsp;file&nbsp;download</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$filename&nbsp;Filename</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string|null&nbsp;$contentType&nbsp;Content&nbsp;type&nbsp;(uses&nbsp;default&nbsp;if&nbsp;null)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">sendHeaders</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$filename</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$contentType</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$contentType</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$contentType</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getMimeType</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Clean&nbsp;any&nbsp;previous&nbsp;output</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">ob_get_level</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">ob_end_clean</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Send&nbsp;headers</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">header</span><span class="keyword">(</span><span class="default">'Content-Disposition:&nbsp;attachment;&nbsp;filename=&quot;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$filename</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'&quot;'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">header</span><span class="keyword">(</span><span class="default">'Content-Type:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$contentType</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">header</span><span class="keyword">(</span><span class="default">'Content-Transfer-Encoding:&nbsp;binary'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="115" href="#115">115</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">header</span><span class="keyword">(</span><span class="default">'Cache-Control:&nbsp;must-revalidate,&nbsp;post-check=0,&nbsp;pre-check=0'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="116" href="#116">116</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">header</span><span class="keyword">(</span><span class="default">'Pragma:&nbsp;public'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="117" href="#117">117</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">header</span><span class="keyword">(</span><span class="default">'Expires:&nbsp;0'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="118" href="#118">118</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="119" href="#119">119</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="120" href="#120">120</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="121" href="#121">121</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Handle&nbsp;export&nbsp;error</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="122" href="#122">122</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="123" href="#123">123</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$message&nbsp;Error&nbsp;message</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="124" href="#124">124</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;int&nbsp;$statusCode&nbsp;HTTP&nbsp;status&nbsp;code&nbsp;to&nbsp;use&nbsp;(default:&nbsp;400)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="125" href="#125">125</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="126" href="#126">126</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="127" href="#127">127</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">handleExportError</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$message</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">int</span><span class="default">&nbsp;</span><span class="default">$statusCode</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">400</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="128" href="#128">128</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="129" href="#129">129</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Log&nbsp;the&nbsp;error&nbsp;if&nbsp;logger&nbsp;is&nbsp;available</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 130" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="130" href="#130">130</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">isset</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="keyword">[</span><span class="default">'logger'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 131" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="131" href="#131">131</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="keyword">[</span><span class="default">'logger'</span><span class="keyword">]</span><span class="default">-&gt;</span><span class="default">error</span><span class="keyword">(</span><span class="default">'Export&nbsp;error&nbsp;('</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getFormatName</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'):&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$message</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="132" href="#132">132</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="133" href="#133">133</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="134" href="#134">134</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Set&nbsp;error&nbsp;message&nbsp;in&nbsp;registry&nbsp;for&nbsp;AJAX&nbsp;response</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 135" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="135" href="#135">135</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="default">-&gt;</span><span class="default">set</span><span class="keyword">(</span><span class="default">'ajax_result'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 136" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="136" href="#136">136</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'error'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$message</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 137" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="137" href="#137">137</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'status'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'error'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 138" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="138" href="#138">138</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'format'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getFormatName</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 139" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="139" href="#139">139</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'timestamp'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">date</span><span class="keyword">(</span><span class="default">'Y-m-d&nbsp;H:i:s'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 140" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="140" href="#140">140</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="141" href="#141">141</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="142" href="#142">142</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;If&nbsp;this&nbsp;is&nbsp;an&nbsp;AJAX&nbsp;request,&nbsp;send&nbsp;appropriate&nbsp;headers</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 143" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="143" href="#143">143</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$_SERVER</span><span class="keyword">[</span><span class="default">'HTTP_X_REQUESTED_WITH'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 144" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="144" href="#144">144</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">strtolower</span><span class="keyword">(</span><span class="default">$_SERVER</span><span class="keyword">[</span><span class="default">'HTTP_X_REQUESTED_WITH'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">'xmlhttprequest'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="145" href="#145">145</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 146" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="146" href="#146">146</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">http_response_code</span><span class="keyword">(</span><span class="default">$statusCode</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 147" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="147" href="#147">147</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">header</span><span class="keyword">(</span><span class="default">'Content-Type:&nbsp;application/json'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="148" href="#148">148</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">echo</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="default">-&gt;</span><span class="default">get</span><span class="keyword">(</span><span class="default">'ajax_result'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="149" href="#149">149</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">exit</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="150" href="#150">150</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="151" href="#151">151</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="152" href="#152">152</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;For&nbsp;non-AJAX&nbsp;requests,&nbsp;we'll&nbsp;let&nbsp;the&nbsp;controller&nbsp;handle&nbsp;the&nbsp;response</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="153" href="#153">153</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="154" href="#154">154</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="155" href="#155">155</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="156" href="#156">156</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;record&nbsp;headers</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="157" href="#157">157</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="158" href="#158">158</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;mixed&nbsp;$record&nbsp;Record&nbsp;to&nbsp;extract&nbsp;headers&nbsp;from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="159" href="#159">159</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array&nbsp;Array&nbsp;of&nbsp;headers</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="160" href="#160">160</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="161" href="#161">161</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getRecordHeaders</span><span class="keyword">(</span><span class="default">$record</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="162" href="#162">162</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 163" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetVarsMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetAllMethod&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="163" href="#163">163</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$headers</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="164" href="#164">164</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 165" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetVarsMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetAllMethod&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="165" href="#165">165</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">method_exists</span><span class="keyword">(</span><span class="default">$record</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'getAll'</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 166" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetAllMethod&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="166" href="#166">166</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$data</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$record</span><span class="default">-&gt;</span><span class="default">getAll</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 167" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetAllMethod&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="167" href="#167">167</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$headers</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">array_keys</span><span class="keyword">(</span><span class="default">$data</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 168" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetVarsMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithArray&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="168" href="#168">168</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">elseif</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">is_object</span><span class="keyword">(</span><span class="default">$record</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">method_exists</span><span class="keyword">(</span><span class="default">$record</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'getVars'</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 169" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetVarsMethod&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="169" href="#169">169</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vars</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$record</span><span class="default">-&gt;</span><span class="default">getVars</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 170" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetVarsMethod&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="170" href="#170">170</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$headers</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">array_keys</span><span class="keyword">(</span><span class="default">$vars</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 171" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithArray&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="171" href="#171">171</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">elseif</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">is_array</span><span class="keyword">(</span><span class="default">$record</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 172" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithArray&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="172" href="#172">172</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$headers</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">array_keys</span><span class="keyword">(</span><span class="default">$record</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="173" href="#173">173</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="174" href="#174">174</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 175" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetVarsMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetAllMethod&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="175" href="#175">175</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$headers</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="176" href="#176">176</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="177" href="#177">177</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="178" href="#178">178</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="179" href="#179">179</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;{@inheritdoc}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="180" href="#180">180</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="181" href="#181">181</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getFormatOptions</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="182" href="#182">182</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 183" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testGetFormatOptions&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="183" href="#183">183</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="184" href="#184">184</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="185" href="#185">185</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="186" href="#186">186</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="187" href="#187">187</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Validate&nbsp;file&nbsp;parameter&nbsp;and&nbsp;prepare&nbsp;save&nbsp;target</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="188" href="#188">188</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="189" href="#189">189</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string|resource&nbsp;$file&nbsp;The&nbsp;file&nbsp;path,&nbsp;PHP&nbsp;stream&nbsp;wrapper,&nbsp;or&nbsp;file&nbsp;pointer</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="190" href="#190">190</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;string|resource&nbsp;The&nbsp;validated&nbsp;save&nbsp;target</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="191" href="#191">191</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;\Exception&nbsp;If&nbsp;the&nbsp;file&nbsp;parameter&nbsp;is&nbsp;invalid</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="192" href="#192">192</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="193" href="#193">193</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">validateAndPrepareSaveTarget</span><span class="keyword">(</span><span class="default">$file</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="194" href="#194">194</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="53 tests cover line 195" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUSDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidDirectory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithWrongResourceType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="195" href="#195">195</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">is_string</span><span class="keyword">(</span><span class="default">$file</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="196" href="#196">196</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Handle&nbsp;string&nbsp;input&nbsp;(file&nbsp;path&nbsp;or&nbsp;PHP&nbsp;stream&nbsp;wrapper)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="45 tests cover line 197" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUSDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidDirectory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="197" href="#197">197</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">validateStringTarget</span><span class="keyword">(</span><span class="default">$file</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="8 tests cover line 198" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithWrongResourceType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="198" href="#198">198</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">elseif</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">is_resource</span><span class="keyword">(</span><span class="default">$file</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="199" href="#199">199</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Handle&nbsp;file&nbsp;pointer</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 200" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithWrongResourceType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="200" href="#200">200</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">validateFilePointer</span><span class="keyword">(</span><span class="default">$file</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="201" href="#201">201</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">else</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 202" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="202" href="#202">202</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">Exception</span><span class="keyword">(</span><span class="default">'File&nbsp;parameter&nbsp;must&nbsp;be&nbsp;either&nbsp;a&nbsp;string&nbsp;(file&nbsp;path/stream&nbsp;wrapper)&nbsp;or&nbsp;a&nbsp;resource&nbsp;(file&nbsp;pointer)'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="203" href="#203">203</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="204" href="#204">204</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="205" href="#205">205</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="206" href="#206">206</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="207" href="#207">207</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Validate&nbsp;string&nbsp;target&nbsp;(file&nbsp;path&nbsp;or&nbsp;PHP&nbsp;stream&nbsp;wrapper)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="208" href="#208">208</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="209" href="#209">209</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$target&nbsp;The&nbsp;file&nbsp;path&nbsp;or&nbsp;stream&nbsp;wrapper&nbsp;to&nbsp;validate</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="210" href="#210">210</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;string&nbsp;The&nbsp;validated&nbsp;target</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="211" href="#211">211</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;\Exception&nbsp;If&nbsp;the&nbsp;target&nbsp;is&nbsp;invalid</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="212" href="#212">212</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="213" href="#213">213</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">validateStringTarget</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$target</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="214" href="#214">214</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="215" href="#215">215</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Check&nbsp;if&nbsp;it's&nbsp;a&nbsp;PHP&nbsp;stream&nbsp;wrapper</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="46 tests cover line 216" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUSDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidDirectory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="216" href="#216">216</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">strpos</span><span class="keyword">(</span><span class="default">$target</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'://'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">false</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 217" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="217" href="#217">217</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">validatePhpStreamWrapper</span><span class="keyword">(</span><span class="default">$target</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="218" href="#218">218</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">else</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="219" href="#219">219</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Handle&nbsp;as&nbsp;regular&nbsp;file&nbsp;path</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="37 tests cover line 220" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUSDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidDirectory&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="220" href="#220">220</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">validateFilePath</span><span class="keyword">(</span><span class="default">$target</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="221" href="#221">221</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="222" href="#222">222</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="223" href="#223">223</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="224" href="#224">224</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="225" href="#225">225</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Validate&nbsp;PHP&nbsp;stream&nbsp;wrapper</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="226" href="#226">226</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="227" href="#227">227</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$wrapper&nbsp;The&nbsp;stream&nbsp;wrapper&nbsp;to&nbsp;validate</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="228" href="#228">228</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;string&nbsp;The&nbsp;validated&nbsp;wrapper</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="229" href="#229">229</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;\Exception&nbsp;If&nbsp;the&nbsp;wrapper&nbsp;is&nbsp;invalid&nbsp;or&nbsp;not&nbsp;supported</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="230" href="#230">230</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="231" href="#231">231</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">validatePhpStreamWrapper</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$wrapper</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="232" href="#232">232</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 233" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="233" href="#233">233</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$wrapper</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">strtolower</span><span class="keyword">(</span><span class="default">$wrapper</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="234" href="#234">234</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="235" href="#235">235</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;List&nbsp;of&nbsp;supported&nbsp;PHP&nbsp;stream&nbsp;wrappers&nbsp;for&nbsp;export</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 236" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="236" href="#236">236</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$supportedWrappers</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 237" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="237" href="#237">237</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'php://output'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 238" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="238" href="#238">238</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'php://memory'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 239" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="239" href="#239">239</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'php://temp'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 240" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="240" href="#240">240</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'php://stdout'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 241" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="241" href="#241">241</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'php://stderr'</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 242" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="242" href="#242">242</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="243" href="#243">243</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="244" href="#244">244</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Check&nbsp;for&nbsp;php://temp&nbsp;with&nbsp;parameters&nbsp;(e.g.,&nbsp;php://temp/maxmemory:1048576)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 245" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="245" href="#245">245</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">strpos</span><span class="keyword">(</span><span class="default">$wrapper</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'php://temp'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">0</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 246" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="246" href="#246">246</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$wrapper</span><span class="keyword">;</span><span class="default">&nbsp;</span><span class="comment">//&nbsp;Allow&nbsp;php://temp&nbsp;with&nbsp;parameters</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="247" href="#247">247</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="248" href="#248">248</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="249" href="#249">249</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Check&nbsp;for&nbsp;exact&nbsp;matches</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 250" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="250" href="#250">250</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">in_array</span><span class="keyword">(</span><span class="default">$wrapper</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$supportedWrappers</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 251" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="251" href="#251">251</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$wrapper</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="252" href="#252">252</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="253" href="#253">253</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="254" href="#254">254</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Check&nbsp;for&nbsp;other&nbsp;registered&nbsp;stream&nbsp;wrappers</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 255" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="255" href="#255">255</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$registeredWrappers</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">stream_get_wrappers</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 256" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="256" href="#256">256</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$scheme</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">parse_url</span><span class="keyword">(</span><span class="default">$wrapper</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">PHP_URL_SCHEME</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="257" href="#257">257</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 258" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="258" href="#258">258</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$scheme</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">in_array</span><span class="keyword">(</span><span class="default">$scheme</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$registeredWrappers</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="259" href="#259">259</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;It's&nbsp;a&nbsp;registered&nbsp;wrapper,&nbsp;allow&nbsp;it&nbsp;but&nbsp;warn&nbsp;about&nbsp;potential&nbsp;issues</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="260" href="#260">260</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$wrapper</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="261" href="#261">261</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="262" href="#262">262</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 263" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="263" href="#263">263</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">Exception</span><span class="keyword">(</span><span class="string">&quot;</span><span class="string">Unsupported&nbsp;stream&nbsp;wrapper:&nbsp;</span><span class="string">{</span><span class="string">$wrapper</span><span class="keyword">}</span><span class="string">.&nbsp;Supported&nbsp;wrappers:&nbsp;</span><span class="string">&quot;</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">implode</span><span class="keyword">(</span><span class="default">',&nbsp;'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$supportedWrappers</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="264" href="#264">264</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="265" href="#265">265</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="266" href="#266">266</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="267" href="#267">267</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Validate&nbsp;regular&nbsp;file&nbsp;path</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="268" href="#268">268</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="269" href="#269">269</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$filePath&nbsp;The&nbsp;file&nbsp;path&nbsp;to&nbsp;validate</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="270" href="#270">270</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;string&nbsp;The&nbsp;validated&nbsp;file&nbsp;path</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="271" href="#271">271</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;\Exception&nbsp;If&nbsp;the&nbsp;file&nbsp;path&nbsp;is&nbsp;invalid</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="272" href="#272">272</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="273" href="#273">273</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">validateFilePath</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$filePath</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="274" href="#274">274</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="37 tests cover line 275" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUSDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidDirectory&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="275" href="#275">275</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$directory</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">dirname</span><span class="keyword">(</span><span class="default">$filePath</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="276" href="#276">276</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="37 tests cover line 277" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUSDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidDirectory&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="277" href="#277">277</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">is_dir</span><span class="keyword">(</span><span class="default">$directory</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 278" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidDirectory&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="278" href="#278">278</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">Exception</span><span class="keyword">(</span><span class="string">&quot;</span><span class="string">Directory&nbsp;does&nbsp;not&nbsp;exist:&nbsp;</span><span class="string">{</span><span class="string">$directory</span><span class="keyword">}</span><span class="string">&quot;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="279" href="#279">279</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="280" href="#280">280</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="33 tests cover line 281" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUSDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="281" href="#281">281</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">is_writable</span><span class="keyword">(</span><span class="default">$directory</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="282" href="#282">282</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">Exception</span><span class="keyword">(</span><span class="string">&quot;</span><span class="string">Directory&nbsp;is&nbsp;not&nbsp;writable:&nbsp;</span><span class="string">{</span><span class="string">$directory</span><span class="keyword">}</span><span class="string">&quot;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="283" href="#283">283</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="284" href="#284">284</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="33 tests cover line 285" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUSDateFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="285" href="#285">285</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$filePath</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="286" href="#286">286</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="287" href="#287">287</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="288" href="#288">288</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="289" href="#289">289</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Validate&nbsp;file&nbsp;pointer</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="290" href="#290">290</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="291" href="#291">291</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;resource&nbsp;$filePointer&nbsp;The&nbsp;file&nbsp;pointer&nbsp;to&nbsp;validate</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="292" href="#292">292</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;resource&nbsp;The&nbsp;validated&nbsp;file&nbsp;pointer</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="293" href="#293">293</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;\Exception&nbsp;If&nbsp;the&nbsp;file&nbsp;pointer&nbsp;is&nbsp;invalid</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="294" href="#294">294</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="295" href="#295">295</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">validateFilePointer</span><span class="keyword">(</span><span class="default">$filePointer</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="296" href="#296">296</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 297" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithWrongResourceType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="297" href="#297">297</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">is_resource</span><span class="keyword">(</span><span class="default">$filePointer</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="298" href="#298">298</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">Exception</span><span class="keyword">(</span><span class="default">'Invalid&nbsp;file&nbsp;pointer&nbsp;provided'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="299" href="#299">299</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="300" href="#300">300</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 301" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithWrongResourceType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="301" href="#301">301</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$resourceType</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">get_resource_type</span><span class="keyword">(</span><span class="default">$filePointer</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 302" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithWrongResourceType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="302" href="#302">302</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$resourceType</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">'stream'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 303" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithWrongResourceType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="303" href="#303">303</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">Exception</span><span class="keyword">(</span><span class="string">&quot;</span><span class="string">Invalid&nbsp;resource&nbsp;type:&nbsp;</span><span class="string">{</span><span class="string">$resourceType</span><span class="keyword">}</span><span class="string">.&nbsp;Expected&nbsp;'stream'</span><span class="string">&quot;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="304" href="#304">304</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="305" href="#305">305</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="306" href="#306">306</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Check&nbsp;if&nbsp;the&nbsp;stream&nbsp;is&nbsp;writable</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 307" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="307" href="#307">307</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$meta</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">stream_get_meta_data</span><span class="keyword">(</span><span class="default">$filePointer</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 308" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="308" href="#308">308</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$mode</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$meta</span><span class="keyword">[</span><span class="default">'mode'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="309" href="#309">309</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 310" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="310" href="#310">310</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">preg_match</span><span class="keyword">(</span><span class="default">'/[wa+]/'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$mode</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 311" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="311" href="#311">311</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">Exception</span><span class="keyword">(</span><span class="string">&quot;</span><span class="string">File&nbsp;pointer&nbsp;is&nbsp;not&nbsp;writable.&nbsp;Mode:&nbsp;</span><span class="string">{</span><span class="string">$mode</span><span class="keyword">}</span><span class="string">&quot;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="312" href="#312">312</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="313" href="#313">313</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 314" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="314" href="#314">314</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$filePointer</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="315" href="#315">315</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="316" href="#316">316</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Fri Jun 20 10:39:00 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/popper.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/bootstrap.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/file.js?v=9.2.32" type="text/javascript"></script>
 </body>
</html>
