<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Adapter</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item active">Adapter</li>
         <li class="breadcrumb-item">(<a href="dashboard.html">Dashboard</a>)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="9"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="3"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="85.36" aria-valuemin="0" aria-valuemax="100" style="width: 85.36%">
           <span class="sr-only">85.36% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">85.36%</div></td>
       <td class="success small"><div align="right">822&nbsp;/&nbsp;963</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="60.00" aria-valuemin="0" aria-valuemax="100" style="width: 60.00%">
           <span class="sr-only">60.00% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">60.00%</div></td>
       <td class="warning small"><div align="right">54&nbsp;/&nbsp;90</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;4</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="AbstractExportFormatAdapter.php.html">AbstractExportFormatAdapter.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="83.70" aria-valuemin="0" aria-valuemax="100" style="width: 83.70%">
           <span class="sr-only">83.70% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">83.70%</div></td>
       <td class="success small"><div align="right">77&nbsp;/&nbsp;92</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="58.33" aria-valuemin="0" aria-valuemax="100" style="width: 58.33%">
           <span class="sr-only">58.33% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">58.33%</div></td>
       <td class="warning small"><div align="right">7&nbsp;/&nbsp;12</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="CsvExportFormatAdapter.php.html">CsvExportFormatAdapter.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="95.98" aria-valuemin="0" aria-valuemax="100" style="width: 95.98%">
           <span class="sr-only">95.98% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">95.98%</div></td>
       <td class="success small"><div align="right">167&nbsp;/&nbsp;174</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="75.00" aria-valuemin="0" aria-valuemax="100" style="width: 75.00%">
           <span class="sr-only">75.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">75.00%</div></td>
       <td class="success small"><div align="right">15&nbsp;/&nbsp;20</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExcelExportFormatAdapter.php.html">ExcelExportFormatAdapter.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="77.11" aria-valuemin="0" aria-valuemax="100" style="width: 77.11%">
           <span class="sr-only">77.11% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">77.11%</div></td>
       <td class="success small"><div align="right">384&nbsp;/&nbsp;498</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="46.15" aria-valuemin="0" aria-valuemax="100" style="width: 46.15%">
           <span class="sr-only">46.15% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">46.15%</div></td>
       <td class="warning small"><div align="right">18&nbsp;/&nbsp;39</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class=""><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportFormatAdapterInterface.php.html">ExportFormatAdapterInterface.php</a></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="JsonExportFormatAdapter.php.html">JsonExportFormatAdapter.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="97.49" aria-valuemin="0" aria-valuemax="100" style="width: 97.49%">
           <span class="sr-only">97.49% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">97.49%</div></td>
       <td class="success small"><div align="right">194&nbsp;/&nbsp;199</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="73.68" aria-valuemin="0" aria-valuemax="100" style="width: 73.68%">
           <span class="sr-only">73.68% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">73.68%</div></td>
       <td class="success small"><div align="right">14&nbsp;/&nbsp;19</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>


     </tbody>
    </table>
   </div>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="danger"><strong>Low</strong>: 0% to 35%</span>
     <span class="warning"><strong>Medium</strong>: 35% to 70%</span>
     <span class="success"><strong>High</strong>: 70% to 100%</span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Fri Jun 20 10:39:00 UTC 2025.</small>
    </p>
   </footer>
  </div>
 </body>
</html>
